#!/usr/bin/env python3
"""
测试重启前内存收集功能
模拟重启检测并收集重启前的内存使用情况
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class MemoryAnalyzer:
    def __init__(self):
        pass
    
    def get_memory_before_restart(self, memory_data: List[Tuple], 
                                restart_time: float, 
                                lookback_minutes: int = 5) -> Optional[float]:
        """获取重启前最后一个有效的内存数据点"""
        lookback_seconds = lookback_minutes * 60
        lookback_start = restart_time - lookback_seconds
        
        # 查找重启前时间窗口内的内存数据
        memory_before = None
        for timestamp, memory_value in reversed(memory_data):
            if lookback_start <= timestamp < restart_time:
                if memory_value > 0:  # 过滤异常值
                    memory_before = memory_value
                    break
        
        return memory_before
    
    def analyze_memory_before_restarts(self, memory_data: List[Tuple], 
                                     restart_events: List[float]) -> Dict:
        """分析重启前的内存使用情况"""
        memory_values = []
        
        for restart_time in restart_events:
            memory_before = self.get_memory_before_restart(memory_data, restart_time)
            if memory_before is not None:
                memory_values.append(memory_before)
        
        if memory_values:
            return {
                'min_memory': min(memory_values),
                'max_memory': max(memory_values),
                'avg_memory': sum(memory_values) / len(memory_values),
                'memory_count': len(memory_values),
                'memory_values': memory_values
            }
        else:
            return {
                'min_memory': None,
                'max_memory': None,
                'avg_memory': None,
                'memory_count': 0,
                'memory_values': []
            }

def format_memory_size(bytes_value: float) -> str:
    """格式化内存大小"""
    if bytes_value < 1024:
        return f"{bytes_value:.1f}B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value/1024:.1f}KB"
    else:
        return f"{bytes_value/1024/1024:.1f}MB"

def format_memory_stats(stats: Dict) -> str:
    """格式化内存统计为显示字符串"""
    if stats['memory_count'] == 0:
        return "-"
    
    min_str = format_memory_size(stats['min_memory'])
    max_str = format_memory_size(stats['max_memory'])
    avg_str = format_memory_size(stats['avg_memory'])
    return f"{min_str}/{max_str}/{avg_str}"

def simulate_restart_detection(uptime_data: List[Tuple]) -> List[float]:
    """模拟重启检测，查找uptime下降的时间点"""
    restart_events = []
    
    for i in range(1, len(uptime_data)):
        prev_timestamp, prev_uptime = uptime_data[i-1]
        curr_timestamp, curr_uptime = uptime_data[i]
        
        # 如果uptime显著下降，认为是重启
        if curr_uptime < prev_uptime * 0.8:  # 下降超过20%
            restart_events.append(curr_timestamp)
            print(f"  检测到重启: {datetime.fromtimestamp(curr_timestamp).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"    uptime从 {prev_uptime:.0f}秒 降到 {curr_uptime:.0f}秒")
    
    return restart_events

def main():
    print("🔍 测试重启前内存收集功能")
    print("=" * 50)
    
    # 模拟一些测试数据
    print("1. 生成模拟数据...")
    
    # 模拟时间序列（过去6小时，每分钟一个数据点）
    base_time = datetime.now().timestamp() - 6 * 3600  # 6小时前
    
    # 模拟uptime数据（包含一次重启）
    uptime_data = []
    memory_data = []
    
    for i in range(360):  # 6小时 * 60分钟
        timestamp = base_time + i * 60
        
        if i < 180:  # 前3小时，正常运行
            uptime = 10800 + i * 60  # 从3小时开始，每分钟增加60秒
            memory = 45 * 1024 * 1024 + (i % 10) * 1024 * 1024  # 45MB左右波动
        elif i == 180:  # 第180分钟发生重启
            uptime = 60  # 重启后从60秒开始
            memory = 42 * 1024 * 1024  # 重启后内存稍微降低
        else:  # 重启后正常运行
            uptime = 60 + (i - 180) * 60  # 从重启后开始计时
            memory = 42 * 1024 * 1024 + ((i - 180) % 8) * 1024 * 1024  # 42MB左右波动
        
        uptime_data.append((timestamp, uptime))
        memory_data.append((timestamp, memory))
    
    print(f"✅ 生成了 {len(uptime_data)} 个数据点")
    
    # 检测重启事件
    print("\n2. 检测重启事件...")
    restart_events = simulate_restart_detection(uptime_data)
    print(f"✅ 检测到 {len(restart_events)} 次重启")
    
    if not restart_events:
        print("❌ 未检测到重启事件，无法测试内存收集功能")
        return
    
    # 分析重启前内存
    print("\n3. 分析重启前内存使用情况...")
    analyzer = MemoryAnalyzer()
    
    for i, restart_time in enumerate(restart_events):
        print(f"\n重启事件 {i+1}:")
        print(f"  重启时间: {datetime.fromtimestamp(restart_time).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 获取重启前内存
        memory_before = analyzer.get_memory_before_restart(memory_data, restart_time)
        if memory_before:
            print(f"  重启前内存: {format_memory_size(memory_before)}")
        else:
            print(f"  重启前内存: 未找到有效数据")
    
    # 整体统计
    print("\n4. 整体内存统计...")
    stats = analyzer.analyze_memory_before_restarts(memory_data, restart_events)
    
    print(f"重启前内存统计:")
    print(f"  数据点数量: {stats['memory_count']}")
    if stats['memory_count'] > 0:
        print(f"  最小值: {format_memory_size(stats['min_memory'])}")
        print(f"  最大值: {format_memory_size(stats['max_memory'])}")
        print(f"  平均值: {format_memory_size(stats['avg_memory'])}")
        print(f"  格式化输出: {format_memory_stats(stats)}")
        
        print(f"\n  所有重启前内存值:")
        for i, mem_val in enumerate(stats['memory_values']):
            print(f"    重启{i+1}: {format_memory_size(mem_val)}")
    
    # 测试边界情况
    print("\n5. 测试边界情况...")
    
    # 测试无重启情况
    no_restart_stats = analyzer.analyze_memory_before_restarts(memory_data, [])
    print(f"无重启情况: {format_memory_stats(no_restart_stats)}")
    
    # 测试重启时间超出数据范围
    future_restart = base_time + 7 * 3600  # 7小时后（超出数据范围）
    future_stats = analyzer.analyze_memory_before_restarts(memory_data, [future_restart])
    print(f"重启时间超出范围: {format_memory_stats(future_stats)}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()

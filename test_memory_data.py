#!/usr/bin/env python3
"""
测试内存数据获取脚本
验证从Prometheus获取设备内存时序数据的功能
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

class PrometheusClient:
    def __init__(self, base_url: str = "http://192.168.0.25:9090"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 10
    
    def test_connection(self) -> bool:
        """测试Prometheus连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/status/config")
            return response.status_code == 200
        except Exception as e:
            print(f"连接测试失败: {e}")
            return False
    
    def get_devices(self, name_pattern: str) -> List[Dict]:
        """获取匹配的设备列表"""
        try:
            query = f'dev_mem_ustest{{name=~"{name_pattern}",type="uptime"}}'
            response = self.session.get(
                f"{self.base_url}/api/v1/query",
                params={"query": query}
            )
            response.raise_for_status()
            data = response.json()
            
            devices = []
            for result in data['data']['result']:
                metric = result['metric']
                devices.append({
                    'name': metric['name'],
                    'sn': metric['sn'],
                    'instance': metric['instance']
                })
            
            return devices
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            return []
    
    def get_uptime_data(self, devices: List[Dict], hours: int = 1) -> Dict[str, List[Tuple]]:
        """获取设备uptime时序数据"""
        try:
            device_names = [d['name'] for d in devices]
            name_pattern = '|'.join(device_names)
            
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)
            
            query = f'dev_mem_ustest{{name=~"({name_pattern})",type="uptime"}}'
            response = self.session.get(
                f"{self.base_url}/api/v1/query_range",
                params={
                    "query": query,
                    "start": start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "end": end_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "step": "60s"
                }
            )
            response.raise_for_status()
            data = response.json()
            
            result = {}
            for series in data['data']['result']:
                device_name = series['metric']['name']
                values = [(float(ts), float(val)) for ts, val in series['values']]
                result[device_name] = values
            
            return result
        except Exception as e:
            print(f"获取uptime数据失败: {e}")
            return {}
    
    def get_memory_data(self, devices: List[Dict], hours: int = 1) -> Dict[str, List[Tuple]]:
        """获取设备内存时序数据"""
        try:
            device_names = [d['name'] for d in devices]
            name_pattern = '|'.join(device_names)
            
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)
            
            query = f'dev_mem_ustest{{name=~"({name_pattern})",type="inner"}}'
            response = self.session.get(
                f"{self.base_url}/api/v1/query_range",
                params={
                    "query": query,
                    "start": start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "end": end_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "step": "60s"
                }
            )
            response.raise_for_status()
            data = response.json()
            
            result = {}
            for series in data['data']['result']:
                device_name = series['metric']['name']
                # 内存数据单位是KB，转换为bytes
                values = [(float(ts), float(val) * 1024) for ts, val in series['values']]
                result[device_name] = values
            
            return result
        except Exception as e:
            print(f"获取内存数据失败: {e}")
            return {}

def format_memory_size(bytes_value: float) -> str:
    """格式化内存大小"""
    if bytes_value < 1024:
        return f"{bytes_value:.1f}B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value/1024:.1f}KB"
    else:
        return f"{bytes_value/1024/1024:.1f}MB"

def main():
    print("🔍 测试内存数据获取功能")
    print("=" * 50)
    
    # 初始化客户端
    client = PrometheusClient()
    
    # 测试连接
    print("1. 测试Prometheus连接...")
    if not client.test_connection():
        print("❌ 连接失败，请检查Prometheus服务")
        return
    print("✅ 连接成功")
    
    # 获取设备列表
    print("\n2. 获取zhongxing设备列表...")
    devices = client.get_devices("zhongxing.*")
    print(f"✅ 找到 {len(devices)} 台设备")
    
    if not devices:
        print("❌ 未找到匹配的设备")
        return
    
    # 显示前5台设备
    print("\n前5台设备:")
    for i, device in enumerate(devices[:5]):
        print(f"  {i+1}. {device['name']} (SN: {device['sn']})")
    
    # 获取uptime数据
    print(f"\n3. 获取过去1小时的uptime数据...")
    uptime_data = client.get_uptime_data(devices[:5], hours=1)  # 只测试前5台
    print(f"✅ 获取到 {len(uptime_data)} 台设备的uptime数据")
    
    # 获取内存数据
    print(f"\n4. 获取过去1小时的内存数据...")
    memory_data = client.get_memory_data(devices[:5], hours=1)  # 只测试前5台
    print(f"✅ 获取到 {len(memory_data)} 台设备的内存数据")
    
    # 分析数据
    print(f"\n5. 数据分析结果:")
    print("-" * 50)
    for device in devices[:5]:
        device_name = device['name']
        print(f"\n设备: {device_name}")
        print(f"SN: {device['sn']}")
        
        # Uptime数据分析
        if device_name in uptime_data:
            uptime_values = uptime_data[device_name]
            print(f"Uptime数据点: {len(uptime_values)}")
            if uptime_values:
                first_uptime = uptime_values[0][1]
                last_uptime = uptime_values[-1][1]
                print(f"  首次uptime: {first_uptime:.0f}秒 ({first_uptime/3600:.1f}小时)")
                print(f"  最后uptime: {last_uptime:.0f}秒 ({last_uptime/3600:.1f}小时)")
        
        # 内存数据分析
        if device_name in memory_data:
            memory_values = memory_data[device_name]
            print(f"内存数据点: {len(memory_values)}")
            if memory_values:
                memory_vals = [val for _, val in memory_values]
                min_mem = min(memory_vals)
                max_mem = max(memory_vals)
                avg_mem = sum(memory_vals) / len(memory_vals)
                
                print(f"  内存统计:")
                print(f"    最小值: {format_memory_size(min_mem)}")
                print(f"    最大值: {format_memory_size(max_mem)}")
                print(f"    平均值: {format_memory_size(avg_mem)}")
        
        print("-" * 30)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()

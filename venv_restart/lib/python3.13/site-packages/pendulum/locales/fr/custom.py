"""
fr custom locale file.
"""
from __future__ import annotations


translations = {
    "units": {"few_second": "quelques secondes"},
    # Relative Time
    "ago": "il y a {0}",
    "from_now": "dans {0}",
    "after": "{0} après",
    "before": "{0} avant",
    # Ordinals
    "ordinal": {"one": "er", "other": "e"},
    # Date formats
    "date_formats": {
        "LTS": "HH:mm:ss",
        "LT": "HH:mm",
        "LLLL": "dddd D MMMM YYYY HH:mm",
        "LLL": "D MMMM YYYY HH:mm",
        "LL": "D MMMM YYYY",
        "L": "DD/MM/YYYY",
    },
}

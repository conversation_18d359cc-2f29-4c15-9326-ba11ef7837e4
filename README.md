# 🔍 设备重启检测脚本

一个简单高效的工具，用于从Prometheus `uptime` 数据中智能检测设备真实重启事件，并过滤数据异常。

## ✨ 功能特点

- 🎯 **智能检测**：准确区分真实重启和数据异常
- 🚀 **快速执行**：处理1500+设备数据 < 10秒
- 📊 **清晰输出**：表格显示重启统计和最后重启时间
- ⚙️ **简单易用**：仅需4个核心参数

## 📦 环境准备

### 1. 创建Python虚拟环境
```bash
# 创建虚拟环境
python3 -m venv venv_restart

# 激活虚拟环境
source venv_restart/bin/activate  # Linux/macOS
# 或
venv_restart\Scripts\activate     # Windows
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

## 🚀 使用方法

### 基本用法
```bash
./check_restarts.py -p "设备名称正则表达式" -t "时间范围"
```

### 参数说明
| 参数 | 说明 | 示例 | 默认值 |
|------|------|------|--------|
| `-p, --pattern` | **必需** 设备名称正则表达式 | `"03_yichang.*"` | - |
| `-t, --time` | 时间范围 | `"24h"`, `"2025-01-30 10:00"` | `"24h"` |
| `--anomaly-tolerance` | 异常容忍窗口 | `"10m"`, `"30m"` | `"20m"` |
| `--url` | Prometheus地址 | `"http://localhost:9090"` | `"http://************:9090"` |

### 时间格式支持
- **相对时间**：`1h`, `6h`, `24h`, `7d`
- **绝对时间**：`"2025-01-30 10:00"`, `"2025-01-30 15:30:45"`

## 📋 使用示例

### 示例1：检查特定设备最近24小时重启情况
```bash
./check_restarts.py -p "03_yichang_PT104E.*"
```

### 示例2：检查所有设备最近3小时重启情况
```bash
./check_restarts.py -p ".*" -t "3h"
```

### 示例3：从指定时间点检查到现在
```bash
./check_restarts.py -p "device.*" -t "2025-01-30 08:00"
```

### 示例4：自定义异常容忍窗口
```bash
./check_restarts.py -p ".*" -t "12h" --anomaly-tolerance "30m"
```

## 📊 输出说明

### 输出格式
```
设备名称                                  SN            真实重启  数据异常  最后重启时间       
device001                                389E80BB4EE9  2         1         2025-01-31 08:30:15
device002                                F2A1B3C4D5E6  0         0         -                  

──────────────────────────────────────────────────────────────────────
时间范围: 2025-01-30 10:00:00 - 2025-01-31 10:00:00
检查设备: 5台 (异常容忍: 20分钟)

总结: 2/5台设备发生真实重启 (40%)
     检测到3次数据异常 (已过滤)
✅ 检测完成
```

### 字段含义
- **设备名称**：设备标识符
- **SN**：设备序列号
- **真实重启**：检测到的真实重启次数
- **数据异常**：过滤掉的数据异常次数
- **最后重启时间**：最近一次重启的时间

## 🧠 检测逻辑

脚本采用智能算法区分真实重启和数据异常：

### 真实重启特征
1. **显著下降**：uptime下降超过80%
2. **低起点恢复**：重启后uptime值很低（<1000秒）
3. **持续低值**：在观察窗口内保持较低水平（<50%）
4. **正常增长**：增长率在合理范围内（0.5-1.5秒/秒）

### 数据异常特征
不满足上述真实重启特征的uptime下降事件，包括：
- 快速恢复到原水平
- 异常的增长模式
- 短暂的数据毛刺

## ⚙️ 配置说明

### 异常容忍窗口
用于观察设备重启后的恢复模式：
- **默认20分钟**：适合大多数设备
- **10分钟**：快速检测，可能误判慢启动设备
- **30分钟**：更保守，适合启动较慢的设备

### Prometheus要求
- 需要 `dev_mem_ustest` 指标
- 包含 `name`、`sn`、`type` 标签
- `type="uptime"` 表示设备运行时间

## 🔧 故障排除

### 常见错误

**连接失败**
```bash
❌ 错误：无法连接到Prometheus服务器
```
- 检查Prometheus地址是否正确
- 确认网络连接正常

**无匹配设备**
```bash
⚠️  警告：未找到匹配的设备
```
- 检查设备名称正则表达式
- 确认Prometheus中有相应数据

**时间格式错误**
```bash
❌ 错误：无效的时间格式
```
- 使用支持的时间格式
- 确保绝对时间不晚于当前时间

### 性能优化
- 使用具体的设备名称模式而不是 `".*"`
- 合理设置时间范围，避免查询过长时间段
- 在网络良好的环境中运行

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！
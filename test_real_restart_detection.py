#!/usr/bin/env python3
"""
测试真实数据的重启检测和内存收集
使用真实的Prometheus数据进行测试
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class PrometheusClient:
    def __init__(self, base_url: str = "http://192.168.0.25:9090"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 10
    
    def get_devices(self, name_pattern: str) -> List[Dict]:
        """获取匹配的设备列表"""
        try:
            query = f'dev_mem_ustest{{name=~"{name_pattern}",type="uptime"}}'
            response = self.session.get(
                f"{self.base_url}/api/v1/query",
                params={"query": query}
            )
            response.raise_for_status()
            data = response.json()
            
            devices = []
            for result in data['data']['result']:
                metric = result['metric']
                devices.append({
                    'name': metric['name'],
                    'sn': metric['sn'],
                    'instance': metric['instance']
                })
            
            return devices
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            return []
    
    def get_time_series_data(self, device_name: str, metric_type: str, hours: int = 24) -> List[Tuple]:
        """获取指定设备的时序数据"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            query = f'dev_mem_ustest{{name="{device_name}",type="{metric_type}"}}'
            response = self.session.get(
                f"{self.base_url}/api/v1/query_range",
                params={
                    "query": query,
                    "start": start_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "end": end_time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                    "step": "60s"
                }
            )
            response.raise_for_status()
            data = response.json()
            
            if data['data']['result']:
                series = data['data']['result'][0]
                if metric_type == "inner":
                    # 内存数据从KB转换为bytes
                    return [(float(ts), float(val) * 1024) for ts, val in series['values']]
                else:
                    # uptime数据保持秒为单位
                    return [(float(ts), float(val)) for ts, val in series['values']]
            
            return []
        except Exception as e:
            print(f"获取{metric_type}数据失败: {e}")
            return []

def detect_restarts(uptime_data: List[Tuple], threshold: float = 0.8) -> List[float]:
    """检测重启事件"""
    restart_events = []
    
    for i in range(1, len(uptime_data)):
        prev_timestamp, prev_uptime = uptime_data[i-1]
        curr_timestamp, curr_uptime = uptime_data[i]
        
        # 如果uptime显著下降，认为是重启
        if prev_uptime > 3600 and curr_uptime < prev_uptime * threshold:  # 前一个uptime > 1小时且下降超过阈值
            restart_events.append(curr_timestamp)
    
    return restart_events

def get_memory_before_restart(memory_data: List[Tuple], restart_time: float, lookback_minutes: int = 5) -> Optional[float]:
    """获取重启前最后一个有效的内存数据点"""
    lookback_seconds = lookback_minutes * 60
    lookback_start = restart_time - lookback_seconds
    
    memory_before = None
    for timestamp, memory_value in reversed(memory_data):
        if lookback_start <= timestamp < restart_time:
            if memory_value > 0:
                memory_before = memory_value
                break
    
    return memory_before

def format_memory_size(bytes_value: float) -> str:
    """格式化内存大小"""
    if bytes_value < 1024:
        return f"{bytes_value:.1f}B"
    elif bytes_value < 1024 * 1024:
        return f"{bytes_value/1024:.1f}KB"
    else:
        return f"{bytes_value/1024/1024:.1f}MB"

def main():
    print("🔍 测试真实数据的重启检测和内存收集")
    print("=" * 60)
    
    # 初始化客户端
    client = PrometheusClient()
    
    # 获取设备列表
    print("1. 获取设备列表...")
    devices = client.get_devices("zhongxing.*")
    print(f"✅ 找到 {len(devices)} 台设备")
    
    if not devices:
        print("❌ 未找到匹配的设备")
        return
    
    # 测试前几台设备
    test_devices = devices[:10]  # 测试前10台设备
    print(f"\n2. 测试前 {len(test_devices)} 台设备的重启检测...")
    
    total_restarts = 0
    devices_with_restarts = 0
    
    for i, device in enumerate(test_devices):
        device_name = device['name']
        print(f"\n--- 设备 {i+1}: {device_name} ---")
        print(f"SN: {device['sn']}")
        
        # 获取过去24小时的uptime数据
        print("  获取uptime数据...")
        uptime_data = client.get_time_series_data(device_name, "uptime", hours=24)
        
        if not uptime_data:
            print("  ❌ 未获取到uptime数据")
            continue
        
        print(f"  ✅ 获取到 {len(uptime_data)} 个uptime数据点")
        
        # 检测重启
        restart_events = detect_restarts(uptime_data)
        
        if not restart_events:
            print("  📊 未检测到重启事件")
            continue
        
        print(f"  🔄 检测到 {len(restart_events)} 次重启:")
        devices_with_restarts += 1
        total_restarts += len(restart_events)
        
        # 获取内存数据
        print("  获取内存数据...")
        memory_data = client.get_time_series_data(device_name, "inner", hours=24)
        
        if not memory_data:
            print("  ❌ 未获取到内存数据")
            continue
        
        print(f"  ✅ 获取到 {len(memory_data)} 个内存数据点")
        
        # 分析每次重启前的内存
        memory_before_restarts = []
        
        for j, restart_time in enumerate(restart_events):
            restart_dt = datetime.fromtimestamp(restart_time)
            print(f"    重启 {j+1}: {restart_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 获取重启前内存
            memory_before = get_memory_before_restart(memory_data, restart_time)
            if memory_before:
                memory_before_restarts.append(memory_before)
                print(f"      重启前内存: {format_memory_size(memory_before)}")
            else:
                print(f"      重启前内存: 未找到有效数据")
        
        # 计算内存统计
        if memory_before_restarts:
            min_mem = min(memory_before_restarts)
            max_mem = max(memory_before_restarts)
            avg_mem = sum(memory_before_restarts) / len(memory_before_restarts)
            
            print(f"  📊 重启前内存统计:")
            print(f"      最小值: {format_memory_size(min_mem)}")
            print(f"      最大值: {format_memory_size(max_mem)}")
            print(f"      平均值: {format_memory_size(avg_mem)}")
            print(f"      格式化: {format_memory_size(min_mem)}/{format_memory_size(max_mem)}/{format_memory_size(avg_mem)}")
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"📈 测试总结:")
    print(f"  测试设备数量: {len(test_devices)}")
    print(f"  有重启的设备: {devices_with_restarts}")
    print(f"  总重启次数: {total_restarts}")
    print(f"  重启率: {devices_with_restarts/len(test_devices)*100:.1f}%")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()

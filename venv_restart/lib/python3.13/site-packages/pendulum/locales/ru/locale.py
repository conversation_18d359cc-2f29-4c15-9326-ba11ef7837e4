from __future__ import annotations

from pendulum.locales.ru.custom import translations as custom_translations


"""
ru locale file.

It has been generated automatically and must not be modified directly.
"""


locale = {
    "plural": lambda n: "few"
    if (
        (
            (0 == 0 and (0 == 0))
            and ((n % 10) == (n % 10) and ((n % 10) >= 2 and (n % 10) <= 4))
        )
        and (not ((n % 100) == (n % 100) and ((n % 100) >= 12 and (n % 100) <= 14)))
    )
    else "many"
    if (
        (
            ((0 == 0 and (0 == 0)) and ((n % 10) == (n % 10) and ((n % 10) == 0)))
            or (
                (0 == 0 and (0 == 0))
                and ((n % 10) == (n % 10) and ((n % 10) >= 5 and (n % 10) <= 9))
            )
        )
        or (
            (0 == 0 and (0 == 0))
            and ((n % 100) == (n % 100) and ((n % 100) >= 11 and (n % 100) <= 14))
        )
    )
    else "one"
    if (
        ((0 == 0 and (0 == 0)) and ((n % 10) == (n % 10) and ((n % 10) == 1)))
        and (not ((n % 100) == (n % 100) and ((n % 100) == 11)))
    )
    else "other",
    "ordinal": lambda n: "other",
    "translations": {
        "days": {
            "abbreviated": {
                0: "пн",
                1: "вт",
                2: "ср",
                3: "чт",
                4: "пт",
                5: "сб",
                6: "вс",
            },
            "narrow": {0: "пн", 1: "вт", 2: "ср", 3: "чт", 4: "пт", 5: "сб", 6: "вс"},
            "short": {0: "пн", 1: "вт", 2: "ср", 3: "чт", 4: "пт", 5: "сб", 6: "вс"},
            "wide": {
                0: "понедельник",
                1: "вторник",
                2: "среда",
                3: "четверг",
                4: "пятница",
                5: "суббота",
                6: "воскресенье",
            },
        },
        "months": {
            "abbreviated": {
                1: "янв.",
                2: "февр.",
                3: "мар.",
                4: "апр.",
                5: "мая",
                6: "июн.",
                7: "июл.",
                8: "авг.",
                9: "сент.",
                10: "окт.",
                11: "нояб.",
                12: "дек.",
            },
            "narrow": {
                1: "Я",
                2: "Ф",
                3: "М",
                4: "А",
                5: "М",
                6: "И",
                7: "И",
                8: "А",
                9: "С",
                10: "О",
                11: "Н",
                12: "Д",
            },
            "wide": {
                1: "января",
                2: "февраля",
                3: "марта",
                4: "апреля",
                5: "мая",
                6: "июня",
                7: "июля",
                8: "августа",
                9: "сентября",
                10: "октября",
                11: "ноября",
                12: "декабря",
            },
        },
        "units": {
            "year": {
                "one": "{0} год",
                "few": "{0} года",
                "many": "{0} лет",
                "other": "{0} года",
            },
            "month": {
                "one": "{0} месяц",
                "few": "{0} месяца",
                "many": "{0} месяцев",
                "other": "{0} месяца",
            },
            "week": {
                "one": "{0} неделя",
                "few": "{0} недели",
                "many": "{0} недель",
                "other": "{0} недели",
            },
            "day": {
                "one": "{0} день",
                "few": "{0} дня",
                "many": "{0} дней",
                "other": "{0} дня",
            },
            "hour": {
                "one": "{0} час",
                "few": "{0} часа",
                "many": "{0} часов",
                "other": "{0} часа",
            },
            "minute": {
                "one": "{0} минута",
                "few": "{0} минуты",
                "many": "{0} минут",
                "other": "{0} минуты",
            },
            "second": {
                "one": "{0} секунда",
                "few": "{0} секунды",
                "many": "{0} секунд",
                "other": "{0} секунды",
            },
            "microsecond": {
                "one": "{0} микросекунда",
                "few": "{0} микросекунды",
                "many": "{0} микросекунд",
                "other": "{0} микросекунды",
            },
        },
        "relative": {
            "year": {
                "future": {
                    "other": "через {0} года",
                    "one": "через {0} год",
                    "few": "через {0} года",
                    "many": "через {0} лет",
                },
                "past": {
                    "other": "{0} года назад",
                    "one": "{0} год назад",
                    "few": "{0} года назад",
                    "many": "{0} лет назад",
                },
            },
            "month": {
                "future": {
                    "other": "через {0} месяца",
                    "one": "через {0} месяц",
                    "few": "через {0} месяца",
                    "many": "через {0} месяцев",
                },
                "past": {
                    "other": "{0} месяца назад",
                    "one": "{0} месяц назад",
                    "few": "{0} месяца назад",
                    "many": "{0} месяцев назад",
                },
            },
            "week": {
                "future": {
                    "other": "через {0} недели",
                    "one": "через {0} неделю",
                    "few": "через {0} недели",
                    "many": "через {0} недель",
                },
                "past": {
                    "other": "{0} недели назад",
                    "one": "{0} неделю назад",
                    "few": "{0} недели назад",
                    "many": "{0} недель назад",
                },
            },
            "day": {
                "future": {
                    "other": "через {0} дня",
                    "one": "через {0} день",
                    "few": "через {0} дня",
                    "many": "через {0} дней",
                },
                "past": {
                    "other": "{0} дня назад",
                    "one": "{0} день назад",
                    "few": "{0} дня назад",
                    "many": "{0} дней назад",
                },
            },
            "hour": {
                "future": {
                    "other": "через {0} часа",
                    "one": "через {0} час",
                    "few": "через {0} часа",
                    "many": "через {0} часов",
                },
                "past": {
                    "other": "{0} часа назад",
                    "one": "{0} час назад",
                    "few": "{0} часа назад",
                    "many": "{0} часов назад",
                },
            },
            "minute": {
                "future": {
                    "other": "через {0} минуты",
                    "one": "через {0} минуту",
                    "few": "через {0} минуты",
                    "many": "через {0} минут",
                },
                "past": {
                    "other": "{0} минуты назад",
                    "one": "{0} минуту назад",
                    "few": "{0} минуты назад",
                    "many": "{0} минут назад",
                },
            },
            "second": {
                "future": {
                    "other": "через {0} секунды",
                    "one": "через {0} секунду",
                    "few": "через {0} секунды",
                    "many": "через {0} секунд",
                },
                "past": {
                    "other": "{0} секунды назад",
                    "one": "{0} секунду назад",
                    "few": "{0} секунды назад",
                    "many": "{0} секунд назад",
                },
            },
        },
        "day_periods": {
            "midnight": "полночь",
            "am": "AM",
            "noon": "полдень",
            "pm": "PM",
            "morning1": "утра",
            "afternoon1": "дня",
            "evening1": "вечера",
            "night1": "ночи",
        },
        "week_data": {
            "min_days": 1,
            "first_day": 0,
            "weekend_start": 5,
            "weekend_end": 6,
        },
    },
    "custom": custom_translations,
}

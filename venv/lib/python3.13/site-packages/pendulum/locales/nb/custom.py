"""
nn custom locale file.
"""
from __future__ import annotations


translations = {
    # Relative time
    "after": "{0} etter",
    "before": "{0} før",
    # Ordinals
    "ordinal": {"one": ".", "two": ".", "few": ".", "other": "."},
    # Date formats
    "date_formats": {
        "LTS": "HH:mm:ss",
        "LT": "HH:mm",
        "LLLL": "dddd Do MMMM YYYY HH:mm",
        "LLL": "Do MMMM YYYY HH:mm",
        "LL": "Do MMMM YYYY",
        "L": "DD.MM.YYYY",
    },
}

# 🏗️ 重启检测脚本 - 详细设计文档

## 📋 概述

基于 `需求-重启检测脚本.md` 的简化需求，设计一个高效、精准的设备重启检测工具。核心目标是**简单易用**、**智能检测**、**快速执行**。

### 🎯 设计目标
- **简洁性**: 单文件，<200行代码，4个核心参数
- **准确性**: 智能区分真实重启和数据异常
- **性能**: <10秒执行时间，高效的数据处理
- **易用性**: 直观的命令行接口和输出格式

---

## 🏛️ 系统架构设计

### 📐 整体架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   命令行接口     │───▶│    主控制器       │───▶│   结果输出       │
│  ArgParser      │    │ CheckRestartsApp │    │ ResultFormatter │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                ┌───────────────────────────────────┐
                │           核心服务层               │
                ├───────────────┬───────────────────┤
                │ PrometheusClient │   TimeParser    │
                │   数据获取       │    时间处理      │
                ├─────────────────┼───────────────────┤
                │ RestartDetector │  DataProcessor   │
                │   重启检测       │    数据处理      │
                └─────────────────┴───────────────────┘
                                │
                                ▼
                ┌───────────────────────────────────┐
                │         Prometheus API            │
                │    dev_mem_ustest 指标数据         │
                └───────────────────────────────────┘
```

### 🧩 模块分工

| 模块 | 职责 | 输入 | 输出 |
|------|------|------|------|
| **CheckRestartsApp** | 主控制器，流程编排 | 命令行参数 | 格式化结果 |
| **PrometheusClient** | Prometheus数据交互 | 查询参数 | 时序数据 |
| **TimeParser** | 时间解析和验证 | 时间字符串 | datetime对象 |
| **RestartDetector** | 重启检测核心算法 | uptime时序数据 | 重启事件列表 |
| **ResultFormatter** | 输出格式化 | 检测结果 | 表格文本 |

---

## 🔧 模块设计详解

### 1️⃣ PrometheusClient - 数据获取模块

**核心功能**:
- 连接测试和健康检查
- 设备发现（基于name正则匹配）
- uptime时序数据批量获取

```python
class PrometheusClient:
    def __init__(self, base_url: str, timeout: int = 10):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = timeout
    
    def test_connection(self) -> bool:
        """测试Prometheus连接"""
        
    def get_devices(self, name_pattern: str) -> List[Device]:
        """通过正则匹配获取设备列表"""
        
    def get_uptime_data(self, devices: List[Device], 
                       start_time: datetime, end_time: datetime) -> Dict[str, TimeSeries]:
        """批量获取设备uptime数据"""
```

**查询优化策略**:
- 使用 `{name=~"pattern"}` 正则过滤，减少网络传输
- 批量查询多设备，避免N+1查询问题
- 合理设置step（默认60s），平衡精度和性能

### 2️⃣ TimeParser - 时间处理模块

**支持格式**:
```python
相对时间：1h, 6h, 24h, 7d
绝对时间：2025-01-30 10:00, 2025-01-30 10:30:45
```

```python
class TimeParser:
    @staticmethod
    def parse_time(time_str: str) -> datetime:
        """解析时间字符串为datetime对象"""
        
    @staticmethod 
    def validate_time(time: datetime) -> bool:
        """验证时间不能大于等于当前时间"""
        
    @staticmethod
    def parse_duration(duration_str: str) -> timedelta:
        """解析异常容忍时间"""
```

**时间处理策略**:
- 使用 `pendulum` 库简化时间操作
- 支持时区感知（默认本地时区）
- 严格验证输入时间范围

### 3️⃣ RestartDetector - 核心检测模块

**检测算法流程**:

```python
class RestartDetector:
    def detect_restarts(self, uptime_data: TimeSeries, 
                       anomaly_tolerance: timedelta) -> List[RestartEvent]:
        """
        核心重启检测算法
        
        算法步骤：
        1. 遍历时序数据，发现uptime下降点
        2. 对每个下降点，分析异常容忍窗口内的行为
        3. 基于恢复模式分类为"真实重启"或"数据异常"
        """
```

**检测逻辑详解**:

```
时间轴:  t1    t2    t3    t4    t5    t6
uptime: 3600 → 100 → 150 → 200 → 250 → 300
                ↑                          
              下降点              异常容忍窗口(20分钟)
                                     
判断逻辑(基于真实重启特征):
- drop_percentage = (3600-100)/3600 = 0.972 (97.2%下降 ✓显著下降)
- uptime_after_drop = 100 < 1000 (✓低起点恢复)
- growth_rate = (300-100)/(t6-t2) ≈ 1.0 (✓正常增长率)
- recovery_ratio = 300/3600 = 0.083 < 0.3 (✓无快速恢复)
- 结论: 真实重启 (满足所有重启特征)

对比数据异常场景:
uptime: 3600 → 0 → 3650 (瞬间恢复)
- drop_percentage = 100% (✓显著下降)
- uptime_after_drop = 0 < 1000 (✓低起点恢复)
- growth_rate = 3650/300 = 12.2 (✗异常高增长率)
- recovery_ratio = 3650/3600 = 1.01 > 0.3 (✗快速恢复)
- 结论: 数据异常 (不满足正常增长率和无快速恢复特征)
```

**重启判断逻辑**:
- **真实重启特征**（正向判断）: 
  - 显著下降（>80%）+ 低起点恢复（<1000秒）+ 正常增长率（0.5-1.5）+ 无快速恢复（<30%）
  - 符合重启后设备从零开始正常运行的模式
- **数据异常特征**（排除判断）: 
  - 不满足真实重启特征的所有uptime下降事件
  - 包括快速恢复、异常增长率、轻微下降等各种数据采集异常
- **容忍窗口**: 默认20分钟，用于观察重启后的恢复模式

### 4️⃣ ResultFormatter - 输出格式化模块

**输出结构设计**:

```python
@dataclass
class FormattedResult:
    device_table: str      # 设备检测结果表格
    summary_info: str      # 汇总统计信息
    time_range: str        # 时间范围信息
```

**表格格式**:
```
设备名称                SN           真实重启  数据异常  最后重启时间
device001              389E80BB4EE9  2        1        2025-01-31 08:30:15
device002              F2A1B3C4D5E6  0        0        -
```

---

## 🌊 数据流程设计

### 📊 数据流图

```
用户输入
   ↓
参数解析 → 时间范围计算 → 设备发现
   ↓             ↓           ↓
错误验证     时间验证    正则匹配设备
   ↓             ↓           ↓
   └─────────→ 数据查询 ←─────┘
                  ↓
             时序数据预处理
                  ↓
             重启检测分析
                  ↓
             结果汇总统计
                  ↓
             格式化输出
```

### 🚀 关键数据结构

```python
# 设备信息
@dataclass
class Device:
    name: str           # 设备名称
    sn: str             # 设备序列号

# 时序数据点
@dataclass
class DataPoint:
    timestamp: datetime  # 时间戳
    value: float        # uptime值

# 重启事件
@dataclass
class RestartEvent:
    timestamp: datetime     # 事件发生时间
    event_type: str        # "restart" | "anomaly"
    uptime_before: float   # 下降前uptime
    uptime_after: float    # 下降后uptime
    recovery_uptime: Optional[float]  # 恢复后uptime（异常事件）

# 设备检测结果
@dataclass
class DeviceResult:
    device: Device
    restart_count: int
    anomaly_count: int
    last_restart_time: Optional[datetime]
    events: List[RestartEvent]
```

---

## 🧠 核心算法设计

### 🔍 重启检测算法

**算法伪代码**:

```python
def detect_restarts(uptime_data, anomaly_tolerance):
    events = []
    
    for i in range(1, len(uptime_data)):
        current_point = uptime_data[i]
        previous_point = uptime_data[i-1]
        
        # 1. 检测uptime下降
        if current_point.value < previous_point.value:
            drop_event = {
                'timestamp': current_point.timestamp,
                'uptime_before': previous_point.value,
                'uptime_after': current_point.value
            }
            
            # 2. 分析异常容忍窗口
            window_end = current_point.timestamp + anomaly_tolerance
            recovery_pattern = analyze_recovery_pattern(
                uptime_data, i, window_end
            )
            
            # 3. 分类事件类型（基于真实重启特征判断）
            if recovery_pattern.is_restart:
                events.append(RestartEvent(
                    event_type='restart',
                    **drop_event
                ))
            else:
                events.append(RestartEvent(
                    event_type='anomaly',
                    **drop_event,
                    recovery_uptime=recovery_pattern.final_uptime
                ))
    
    return events
```

**恢复模式分析**:

```python
def analyze_recovery_pattern(data, drop_index, window_end):
    """
    分析异常容忍窗口内的uptime恢复模式
    
    返回:
    - is_restart: 是否为真实重启（基于重启特征正向判断）
    - is_anomaly: 是否为数据异常（= not is_restart）
    - final_uptime: 窗口结束时的uptime值
    - recovery_ratio: 恢复程度比例
    - growth_rate: 增长趋势斜率 (uptime增长/时间，正常约为1.0)
    """
    
    window_data = [p for p in data[drop_index:] 
                   if p.timestamp <= window_end]
    
    if len(window_data) < 2:
        return RecoveryPattern(is_anomaly=False)
    
    # 计算基础指标
    uptime_before = data[drop_index-1].value
    uptime_after_drop = window_data[0].value
    final_uptime = window_data[-1].value
    
    # 1. 计算恢复程度比例
    recovery_ratio = final_uptime / uptime_before if uptime_before > 0 else 0
    
    # 2. 计算增长趋势斜率 (uptime增长秒数 / 实际经过秒数)
    time_span = (window_data[-1].timestamp - window_data[0].timestamp).total_seconds()
    uptime_growth = final_uptime - uptime_after_drop
    growth_rate = uptime_growth / time_span if time_span > 0 else 0
    
    # 3. 综合判断逻辑
    # 真实重启特征判断（正向判断，更可靠）：
    # 1. 显著下降：uptime下降幅度大（通常>80%）
    # 2. 低起点恢复：重启后从很低的值开始
            # 3. 正常增长：增长率范围0.5-1.5（每秒增长约1秒，符合uptime定义）
    # 4. 持续恢复：没有快速恢复到原水平
    
    drop_percentage = (uptime_before - uptime_after_drop) / uptime_before if uptime_before > 0 else 0
    
    is_significant_drop = drop_percentage > 0.8        # 下降超过80%
    is_low_start = uptime_after_drop < 1000           # 重启后值很低（<1000秒）
    is_normal_growth = 0.5 <= growth_rate <= 1.5      # 正常增长率（接近1.0）
    is_no_quick_recovery = recovery_ratio < 0.3       # 没有快速恢复
    
    # 同时满足多个真实重启特征才判定为真实重启
    is_restart = is_significant_drop and is_low_start and is_normal_growth and is_no_quick_recovery
    
    # 不满足真实重启特征的，判定为数据异常
    is_anomaly = not is_restart
    
    return RecoveryPattern(
        is_restart=is_restart,
        is_anomaly=is_anomaly,
        final_uptime=final_uptime,
        recovery_ratio=recovery_ratio,
        growth_rate=growth_rate
    )
```

### ⚡ 性能优化策略

1. **批量查询优化**:
   ```python
   # ✅ 优化：批量查询
   query = f'dev_mem_ustest{{name=~"{pattern}"}}'
   
   # ❌ 避免：逐个查询
   for device in devices:
       query = f'dev_mem_ustest{{name="{device.name}"}}'
   ```

2. **数据预处理优化**:
   ```python
   # 排序、去重、插值处理
   def preprocess_timeseries(raw_data):
       sorted_data = sorted(raw_data, key=lambda x: x.timestamp)
       deduplicated = remove_duplicates(sorted_data)
       return interpolate_missing_points(deduplicated)
   ```

3. **内存优化**:
   - 使用生成器处理大数据集
   - 及时释放不需要的中间结果
   - 流式处理时序数据

---

## 🔌 接口设计规范

### 📝 命令行接口

```bash
# 标准格式
python3 check_restarts.py -p <pattern> [options]

# 参数说明
-p, --pattern       # 必需，设备名称正则表达式
-t, --time         # 时间范围，默认 "24h"
--anomaly-tolerance # 异常容忍窗口，默认 "20m"  
--url              # Prometheus地址，默认 "http://192.168.0.25:9090"
```

### 🚦 错误码规范

| 错误码 | 含义 | 示例 |
|--------|------|------|
| 0 | 成功执行 | 正常检测完成 |
| 1 | 参数错误 | 正则表达式格式错误 |
| 2 | 连接错误 | Prometheus服务不可达 |
| 3 | 数据错误 | 查询结果为空或格式错误 |
| 4 | 系统错误 | 内存不足等系统级错误 |

### 📤 输出接口规范

**标准输出格式**:
```
设备结果表格
────────────────────────────────────────
时间和统计信息
总结信息
```

**JSON输出格式**（预留扩展）:
```json
{
  "time_range": {
    "start": "2025-01-30T10:00:00",
    "end": "2025-01-31T10:00:00"
  },
  "summary": {
    "total_devices": 5,
    "devices_with_restarts": 2,
    "restart_rate": "40%",
    "total_restarts": 3,
    "total_anomalies": 2
  },
  "devices": [...]
}
```

---

## 🛡️ 错误处理策略

### 🚨 错误分类和处理

**1. 网络和连接错误**:
```python
try:
    response = requests.get(url, timeout=10)
except requests.ConnectionError:
    print("❌ 错误：无法连接到Prometheus服务器")
    sys.exit(2)
except requests.Timeout:
    print("❌ 错误：请求超时，请检查网络连接")
    sys.exit(2)
```

**2. 数据格式错误**:
```python
def parse_prometheus_response(response):
    try:
        data = response.json()
        if 'data' not in data:
            raise ValueError("响应格式错误")
        return data['data']
    except (json.JSONDecodeError, KeyError) as e:
        print(f"❌ 错误：Prometheus响应格式异常: {e}")
        sys.exit(3)
```

**3. 业务逻辑错误**:
```python
def validate_business_logic(devices, time_range):
    if not devices:
        print("⚠️  警告：未找到匹配的设备")
        sys.exit(0)
    
    if time_range.total_seconds() < 300:  # 5分钟
        print("❌ 错误：时间范围过短，建议至少5分钟")
        sys.exit(1)
```

### 📋 错误信息设计原则

- **清晰明确**: 错误信息直接说明问题和解决方法
- **分级处理**: 区分错误、警告、提示信息
- **用户友好**: 避免技术术语，使用简洁中文描述
- **可操作**: 提供具体的解决建议

---

## ⚡ 性能优化方案

### 🎯 性能目标

| 指标 | 目标值 | 测量方法 |
|------|--------|----------|
| **启动时间** | <2秒 | 从命令执行到首次输出 |
| **数据查询** | <5秒 | Prometheus查询和响应解析 |
| **检测处理** | <2秒 | 重启检测算法执行 |
| **总执行时间** | <10秒 | 完整流程端到端 |

### 🚀 优化策略

**1. 查询优化**:
```python
# 批量查询，减少HTTP请求数
query = f'''
    dev_mem_ustest{{name=~"{pattern}"}}
    [range:{start_time}:{end_time}:{step}]
'''

# 合理的step设置
default_step = "60s"  # 平衡精度和性能
```

**2. 数据处理优化**:
```python
# 使用生成器，避免大量内存占用
def process_timeseries_stream(raw_data):
    for device_data in raw_data:
        yield preprocess_device_data(device_data)

# 早期过滤，减少处理量
def filter_relevant_points(points, time_window):
    return [p for p in points if is_in_window(p, time_window)]
```

**3. 算法优化**:
```python
# 滑动窗口算法，避免嵌套循环
def sliding_window_detection(data, window_size):
    for i in range(len(data) - window_size + 1):
        window = data[i:i + window_size]
        yield analyze_window(window)
```

---

## 🛠️ 技术栈选择说明

### 📦 核心依赖

| 库名 | 版本要求 | 选择理由 | 替代方案 |
|------|----------|----------|----------|
| **requests** | >=2.25.0 | HTTP请求简洁、可靠 | urllib (标准库，复杂) |
| **pendulum** | >=2.1.0 | 时间处理直观、强大 | arrow, datetime |

### 🎯 依赖最小化原则

```python
# requirements.txt (仅2个依赖)
requests>=2.25.0
pendulum>=2.1.0

# 可选依赖 (美化输出)
# rich>=10.0.0  # 表格美化，可选
```

### 💡 技术选择对比

**时间库对比**:
```python
# pendulum - 推荐
now = pendulum.now()
start = now.subtract(hours=24)

# arrow - 备选
now = arrow.now()  
start = now.shift(hours=-24)

# datetime - 标准库
now = datetime.now()
start = now - timedelta(hours=24)
```

**HTTP库对比**:
```python
# requests - 推荐
response = requests.get(url, params=params, timeout=10)
data = response.json()

# urllib - 标准库
req = urllib.request.Request(f"{url}?{urllib.parse.urlencode(params)}")
response = urllib.request.urlopen(req, timeout=10)
data = json.loads(response.read())
```

---

## 📋 实现计划和TODO

### 🎯 总体计划

**预计开发时间**: 1-2个工作日  
**代码目标行数**: 150-180行  
**测试覆盖率**: >80%

### 📅 实施阶段

#### Phase 1: 基础框架搭建 (3-4小时)
> 目标：建立项目骨架，完成基本参数解析

- [ ] **P1-1: 项目初始化**
  - 创建 `check_restarts.py` 主文件
  - 设置 `requirements.txt` 依赖文件
  - 建立基本的项目结构和注释规范
  - **验收标准**: 能够执行 `python check_restarts.py --help`

- [ ] **P1-2: 参数解析模块**
  - 实现 `argparse` 配置，支持所有4个核心参数
  - 添加参数验证（正则表达式格式、URL格式等）
  - 实现 `--help` 详细帮助信息
  - **验收标准**: 所有参数能正确解析，错误参数有清晰提示

- [ ] **P1-3: 基础错误处理框架**
  - 定义错误码体系和异常类
  - 实现统一的错误输出格式
  - 添加优雅退出机制
  - **验收标准**: 各种参数错误能正确捕获并友好提示

#### Phase 2: 核心数据获取 (4-5小时)
> 目标：能够从Prometheus查询和解析数据

- [ ] **P2-1: PrometheusClient实现**
  - 实现连接测试 `test_connection()`
  - 实现设备查询 `get_devices()` 支持正则匹配
  - 添加HTTP超时和重试机制
  - **验收标准**: 能连接真实Prometheus并获取设备列表

- [ ] **P2-2: TimeParser时间解析**
  - 实现相对时间解析 (`1h`, `24h`, `7d`)
  - 实现绝对时间解析 (`"2025-01-30 10:00"`)
  - 添加时间验证（不能大于当前时间）
  - **验收标准**: 各种时间格式都能正确解析

- [ ] **P2-3: 数据获取和预处理**
  - 实现 `get_uptime_data()` 批量查询
  - 添加数据排序、去重、验证
  - 优化查询性能（批量查询、合理step）
  - **验收标准**: 能获取真实uptime时序数据并预处理

#### Phase 3: 核心检测算法 (5-6小时)  
> 目标：实现准确的重启检测和异常区分

- [ ] **P3-1: RestartDetector基础实现**
  - 实现uptime下降点检测
  - 实现基本的重启事件记录
  - 添加检测算法的单元测试
  - **验收标准**: 能识别明显的uptime下降事件

- [ ] **P3-2: 异常容忍逻辑实现**
  - 实现异常容忍窗口分析算法
  - 实现恢复模式分析（数据异常 vs 真实重启）
  - 添加容忍参数的可配置性
  - **验收标准**: 能正确区分数据异常和真实重启

- [ ] **P3-3: 事件分类和统计**
  - 实现重启事件和异常事件的分类统计
  - 计算最后重启时间、重启率等统计指标
  - 优化检测算法性能
  - **验收标准**: 统计结果准确，算法性能达标

#### Phase 4: 输出和集成优化 (2-3小时)
> 目标：完整可用的最终版本

- [ ] **P4-1: ResultFormatter输出格式化**
  - 实现标准表格输出格式
  - 实现统计信息和汇总显示
  - 确保输出格式美观、对齐
  - **验收标准**: 输出格式符合需求规范

- [ ] **P4-2: 主流程集成和优化**
  - 整合所有模块，实现端到端流程
  - 添加性能监控和优化
  - 确保总执行时间<10秒
  - **验收标准**: 完整功能运行正常，性能达标

- [ ] **P4-3: 集成测试和文档**
  - 端到端集成测试（多种场景）
  - 完善错误处理和边界情况
  - 添加使用示例和说明注释
  - **验收标准**: 各种使用场景都能正常工作

### 🧪 测试用例设计

#### 单元测试用例

**TimeParser测试**:
```python
def test_time_parser():
    # 相对时间
    assert parse_time("1h") == now - timedelta(hours=1)
    assert parse_time("24h") == now - timedelta(hours=24)
    
    # 绝对时间
    assert parse_time("2025-01-30 10:00") == datetime(2025, 1, 30, 10, 0)
    
    # 错误输入
    with pytest.raises(ValueError):
        parse_time("invalid")
```

**RestartDetector测试**:
```python
def test_restart_detection():
    # 真实重启场景
    uptime_data = [(t1, 3600), (t2, 100), (t3, 200), (t4, 300)]
    events = detect_restarts(uptime_data, tolerance=300)
    assert len(events) == 1
    assert events[0].event_type == "restart"
    
    # 数据异常场景  
    uptime_data = [(t1, 3600), (t2, 0), (t3, 3650)]
    events = detect_restarts(uptime_data, tolerance=300)
    assert events[0].event_type == "anomaly"
```

#### 集成测试场景

1. **正常场景**: 有重启有异常的混合数据
2. **边界场景**: 空数据、单点数据、极短时间范围
3. **异常场景**: Prometheus连接失败、数据格式错误
4. **性能场景**: 大量设备、长时间范围的性能测试

### 🎯 质量保证

**代码质量**:
- Type hints覆盖率 >90%
- 关键算法注释完整
- 变量命名清晰语义化

**文档质量**:
- README使用说明
- 关键函数docstring
- 复杂算法的注释说明

**性能质量**:
- 执行时间<10秒
- 内存使用合理
- 错误处理完善

---

## 📊 验收标准

### ✅ 功能验收

- [ ] 支持所有4个核心参数，默认值正确
- [ ] 能正确连接Prometheus并获取数据
- [ ] 重启检测算法准确区分真实重启和数据异常
- [ ] 输出格式符合需求规范
- [ ] 错误处理友好，有清晰的错误提示

### ⚡ 性能验收

- [ ] 总执行时间 < 10秒 (测试100台设备24小时数据)
- [ ] 代码行数 < 200行
- [ ] 内存使用合理 (< 100MB)
- [ ] 启动时间 < 2秒

### 🛡️ 稳定性验收

- [ ] 各种异常输入都有错误处理
- [ ] 网络异常、数据异常都能优雅处理
- [ ] 边界条件测试通过
- [ ] 长时间运行无内存泄漏

---

*本设计文档将指导 `check_restarts.py` 的完整实现，确保最终产品简洁、高效、可靠。*